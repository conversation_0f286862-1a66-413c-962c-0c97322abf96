services:

  # Eureka Discovery Server
  eureka:
    image: steeltoeoss/eureka-server
    container_name: eureka-server
    ports:
      - "${EUREKA_PORT:-8761}:8761"
    environment:
      - SPRING_CLOUD_NETFLIX_EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_APPLICATION_NAME=eureka-server
    networks:
      - app-net

  # Config Server (native)
  config-server:
    build:
      context: ./config-server
      dockerfile: Dockerfile
    container_name: config-server
    ports:
      - "8888:8888"
    volumes:
      - ./config-repo:/config-repo:ro
    environment:
      # native-backend instead Git/Vault
      SPRING_PROFILES_ACTIVE: native
      SERVER_PORT: 8888
      SPRING_CLOUD_EUREKA_CLIENT_SERVICEURL_DEFAULTZONE: http://eureka-server:8761/eureka
    depends_on:
      - eureka
    healthcheck:
      test: ["CMD-SHELL", "wget -qO- http://localhost:8888/actuator/health | grep UP"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s
    networks:
      - app-net
    restart: on-failure

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: api-gateway-cts
    ports:
      - "${API_GATEWAY_PORT:-8080}:8080"
    environment:
      SPRING_APPLICATION_NAME: "api-gateway"
      SPRING_CONFIG_IMPORT: "optional:configserver:http://config-server:8888"
      SPRING_CLOUD_CONFIG_RETRY_MAX_ATTEMPTS: "6"
      SPRING_CLOUD_CONFIG_RETRY_INITIAL_INTERVAL: "2000"
      EUREKA_SERVER_URL: ${EUREKA_SERVER_URL}
    depends_on:
      config-server:
        condition: service_healthy
      eureka:
        condition: service_started
    healthcheck:
      test: ["CMD-SHELL", "wget -qO- http://localhost:8080/actuator/health | grep UP"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s
    networks:
      - app-net
    restart: on-failure

  # Postgres for user-service
  user-db:
    image: postgres:15
    container_name: cts-user-db
    restart: always
    environment:
      POSTGRES_DB: ${USER_DB_NAME}
      POSTGRES_USER: ${USER_DB_USER}
      POSTGRES_PASSWORD: ${USER_DB_PASSWORD}
    ports:
      - "${USER_DB_PORT_ON_HOST}:5432"
    networks:
      - app-net
    volumes:
      - user_pgdata:/var/lib/postgresql/data

  # User Service
  user-service:
    build:
      context: ./user-service
      dockerfile: Dockerfile
      args:
        MAVEN_USERNAME: ${MAVEN_USERNAME}
        MAVEN_PASSWORD: ${MAVEN_PASSWORD}
    container_name: user-service-cts
    depends_on:
      - user-db
    environment:
      SPRING_DATASOURCE_URL: ******************************/${USER_DB_NAME}
      SPRING_DATASOURCE_USERNAME: ${USER_DB_USER}
      SPRING_DATASOURCE_PASSWORD: ${USER_DB_PASSWORD}
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: ${DB_CLASS_NAME}
      EUREKA_SERVER_URL: ${EUREKA_SERVER_URL}
      MAVEN_USERNAME: ${MAVEN_USERNAME}
      MAVEN_PASSWORD: ${MAVEN_PASSWORD}
      JWT_PUBLIC_KEY: ${JWT_PUBLIC_KEY}
    ports:
      - "${USER_PORT:-8080}:8080"
      - "${USER_GRPC_PORT:-9090}:9090"
    networks:
      - app-net


networks:
  app-net:
    driver: bridge

volumes:
  user_pgdata: