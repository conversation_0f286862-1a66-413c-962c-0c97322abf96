# Eureka. create these variables also in /api-gateway/.env
EUREKA_PORT=8761
EUREKA_SERVER_URL=http://eureka-server:8761/eureka
EUREKA_INSTANCE_HOSTNAME=eureka-server
SPRING_EUREKA_CLIENT_SERVICE-URL_DEFAULTZONE=http://eureka-server:8761/eureka

# API Gateway
API_GATEWAY_PORT=8080


# Database (User-Service)
DB_CLASS_NAME=org.postgresql.Driver
USER_DB_HOST=user-db
USER_DB_PORT=5432
USER_DB_PORT_ON_HOST=5434
USER_DB_URL=jdbc:postgresql://user-db:${USER_DB_PORT}/${USER_DB_NAME}
USER_DB_NAME=user_service
USER_DB_USER=user_service
USER_DB_PASSWORD=user_service

# User-Service port mapping
USER_PORT=8082
USER_GRPC_PORT=9090

JWT_PUBLIC_KEY=-----BEGIN PUBLIC KEY----- MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3miFZUDzHny53Xo4ELkU UHaarTXOyL77vy9XYdeunmuHZbfGyN4Wqivg+QZAGz8c26ux08eJTsttW4EBhI71 atdVW1vnzDCts1i0iqqSOjp2Z4vBb8/O3XtTW2CnwKLuiS7AXqTGoaXKQu+HVeZe K23HVQnrr8YD3jXREx1iVr/4KAWFWQ0SVmXi9pt7EvNgWU8/8J/s0CBxZaJCkgOO B5IY5kdCLMREz9PV/dTA9+gO5SQMcO25xdYrsqbM4Mz76GosaXjd4ezJtNQ2vvly jLTgMRiKNFpYFNURYghwRNJhZBS/Q8GCE8NF/T+QzQif97tOZL5Q4vwlJ6sELwGh pwIDAQAB -----END PUBLIC KEY----- 